在现在的基础上增加两个功能，请求日志与题库管理维护业务。后端仅提供接口，无需制作html等前端页面。使用curl测试通过即可。无需进行鉴权机制，仅实现所需业务即可

- 增加一个请求日志记录表

1. id = 主键，自增
2. app_id = 发起请求的应用的id
3. app_name = 应用的名称
4. user_id = 关联的用户的id
5. image_url = 应用请求时图片url参数
6. response_payload = API返回的数组数据
7. qwen_tokens = qwen模型消耗的token
8. deepseek_tokens = deepseek模型消耗的token
9. created_at = 请求时间
10. status = 响应状态 1=成功 0=失败
11. error_message = 失败时的错误信息
12. is_manual_checked = 人工确认标记 '0=未标记, 1=确认',
13. created_at = 创建时间
14. updated_at = 更新时间
 

 - 请求日志的使用说明

1. qwen与deepseek的输入输出的token消耗需要使用json存入，示例{qwen_input：“1098”，qwen_output:"347"，qwen_total:"1445"}.
2. is_manual_checked 人工标记字段，用户后期管理人员维护的时候对访问日志进行标记核查
3. 超级管理员允许增删改查、题库管理员允许增删改查。




- 题库管理维护业务，根据目前有的题库数据表增加题库管理维护业务。
1. id、qwen_raw、deepseek_raw、qwen_parsed 不允许修改。
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    cache_key_hash VARCHAR(255) NOT NULL COMMENT '被哈希化的缓存键名字',
    question_type VARCHAR(50) NOT NULL COMMENT '问题类型（单选题、多选题、判断题）',
    question_text TEXT NOT NULL COMMENT '问题内容',
    option_a TEXT COMMENT '问题选项A',
    option_b TEXT COMMENT '问题选项B', 
    option_c TEXT COMMENT '问题选项C',
    option_d TEXT COMMENT '问题选项D',
    option_y TEXT COMMENT '问题选项Y（判断题正确）',
    option_n TEXT COMMENT '问题选项N（判断题错误）',
    answer JSON COMMENT '问题答案（JSON格式）',
    analysis TEXT COMMENT '问题解析',
    user_image VARCHAR(500) COMMENT '问题对应的图片名称',
    image_url VARCHAR(1000) NOT NULL COMMENT '用户提交的图片URL地址',
    qwen_raw JSON COMMENT 'Qwen返回的原始数据',
    deepseek_raw JSON COMMENT 'DeepSeek返回的原始数据',
    qwen_parsed JSON COMMENT '被格式化解析后的Qwen数据',
    is_verified TINYINT DEFAULT 0 COMMENT '是否已经验证过，默认值为0',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_cache_key_hash (cache_key_hash) COMMENT '缓存键哈希索引',
    INDEX idx_question_type (question_type) COMMENT '问题类型索引',
    INDEX idx_created_at (created_at) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目信息表';