package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"go-api-solve/internal/config"
	"go-api-solve/internal/handler"
	"go-api-solve/internal/middleware"
	"go-api-solve/internal/repository"
	"go-api-solve/internal/service"
	"go-api-solve/pkg/ai"
	"go-api-solve/pkg/database"
	"go-api-solve/pkg/redis"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()
	
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 初始化数据库连接
	if err := database.InitDB(cfg); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// 自动迁移数据库表
	if err := database.AutoMigrate(); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化Redis连接
	if err := redis.InitRedis(cfg); err != nil {
		log.Fatalf("Failed to initialize Redis: %v", err)
	}
	defer redis.CloseRedis()

	// 初始化AI客户端
	qwenClient := ai.NewQwenClient(cfg.AI.QwenKey)
	deepseekClient := ai.NewDeepseekClient(cfg.AI.DeepseekKey)

	// 初始化仓库层
	db := database.GetDB()
	questionRepo := repository.NewQuestionRepository(db)
	modelConfigRepo := repository.NewModelConfigRepository(db)
	requestLogRepo := repository.NewRequestLogRepository(db)

	// 初始化服务层
	qwenService := service.NewQwenService(qwenClient, modelConfigRepo)
	deepseekService := service.NewDeepseekService(deepseekClient, modelConfigRepo)
	requestLogService := service.NewRequestLogService(requestLogRepo)
	questionService := service.NewQuestionService(questionRepo, qwenService, deepseekService, requestLogService)
	questionMgmtService := service.NewQuestionManagementService(questionRepo)

	// 初始化处理器层
	questionHandler := handler.NewQuestionHandler(questionService)
	requestLogHandler := handler.NewRequestLogHandler(requestLogService)
	questionMgmtHandler := handler.NewQuestionManagementHandler(questionMgmtService)

	// 创建Gin路由器
	router := gin.New()

	// 添加中间件
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.CORS())

	// 注册路由
	setupRoutes(router, questionHandler, requestLogHandler, questionMgmtHandler)

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.Server.Port)
	
	// 优雅关闭
	go func() {
		if err := router.Run(":" + cfg.Server.Port); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Server shutting down...")
}

// setupRoutes 设置路由
func setupRoutes(router *gin.Engine, questionHandler *handler.QuestionHandler, requestLogHandler *handler.RequestLogHandler, questionMgmtHandler *handler.QuestionManagementHandler) {
	// API版本分组
	v1 := router.Group("/api/v1")
	{
		// 健康检查
		v1.GET("/health", questionHandler.HealthCheck)

		// 题目处理接口
		v1.POST("/process-image", questionHandler.ProcessImage)

		// 请求日志管理接口
		v1.GET("/request-logs", requestLogHandler.ListRequestLogs)
		v1.GET("/request-logs/:id", requestLogHandler.GetRequestLog)
		v1.PUT("/request-logs/:id/manual-check", requestLogHandler.UpdateManualCheckStatus)
		v1.GET("/request-logs/statistics", requestLogHandler.GetStatistics)
		v1.POST("/request-logs", requestLogHandler.CreateRequestLog) // 可选，主要用于测试

		// 题库管理接口
		v1.GET("/questions", questionMgmtHandler.ListQuestions)
		v1.GET("/questions/:id", questionMgmtHandler.GetQuestion)
		v1.POST("/questions", questionMgmtHandler.CreateQuestion)
		v1.PUT("/questions/:id", questionMgmtHandler.UpdateQuestion)
		v1.DELETE("/questions/:id", questionMgmtHandler.DeleteQuestion)
	}

	// 根路径重定向到健康检查
	router.GET("/", func(c *gin.Context) {
		c.Redirect(302, "/api/v1/health")
	})
}
