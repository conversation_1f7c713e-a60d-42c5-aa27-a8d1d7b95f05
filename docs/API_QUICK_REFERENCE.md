# API快速参考手册

## 🚀 基础信息

- **API基础URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **默认分页**: 50条/页

## 📊 请求日志API

### 查询日志列表
```
GET /api/v1/request-logs?page=1&page_size=20&status=1
```

### 获取单个日志
```
GET /api/v1/request-logs/{id}
```

### 人工确认
```
PUT /api/v1/request-logs/{id}/manual-check
Body: {"is_manual_checked": 1}
```

### 统计信息
```
GET /api/v1/request-logs/statistics
```

## 📚 题库管理API

### 查询题目列表
```
GET /api/v1/questions?page=1&page_size=20&question_type=单选题
```

### 获取单个题目
```
GET /api/v1/questions/{id}
```

### 创建题目
```
POST /api/v1/questions
Body: {
  "cache_key_hash": "unique_hash",
  "question_type": "单选题",
  "question_text": "题目内容",
  "option_a": "选项A",
  "option_b": "选项B",
  "answer": {"correct": "A"},
  "user_image": "https://example.com/img.jpg"
}
```

### 更新题目
```
PUT /api/v1/questions/{id}
Body: {
  "question_text": "新的题目内容",
  "is_verified": 1
}
```

### 删除题目
```
DELETE /api/v1/questions/{id}
```

## 🔍 查询参数

### 请求日志过滤
- `status`: 0=失败, 1=成功
- `is_manual_checked`: 0=未确认, 1=已确认
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `app_id`: 应用ID
- `user_id`: 用户ID

### 题库过滤
- `question_type`: 单选题/多选题/判断题
- `is_verified`: 0=未验证, 1=已验证
- `keyword`: 搜索关键词

### 通用分页
- `page`: 页码 (默认1)
- `page_size`: 每页大小 (默认50, 最大100)
- `sort`: 排序字段
- `order`: asc/desc

## 📋 字段说明

### 请求日志字段
- `id`: 日志ID
- `app_id`: 应用ID (默认"1")
- `app_name`: 应用名称 (默认"1")
- `user_id`: 用户ID (默认1)
- `image_url`: 图片URL
- `response_payload`: 响应数据
- `qwen_tokens`: Qwen token消耗
- `deepseek_tokens`: DeepSeek token消耗
- `status`: 状态 (0=失败, 1=成功)
- `error_message`: 错误信息
- `is_manual_checked`: 人工确认 (0=未确认, 1=已确认)

### 题目字段
- `question_type`: 题目类型
- `question_text`: 题目内容
- `options`: 选项 (A/B/C/D 或 Y/N)
- `answer`: 答案
- `analysis`: 解析
- `user_image`: 用户图片
- `image_url`: 管理员图片
- `is_verified`: 验证状态 (0=未验证, 1=已验证)

### 不可修改字段
- `id`: 主键ID
- `qwen_raw`: Qwen原始数据
- `deepseek_raw`: DeepSeek原始数据
- `qwen_parsed`: Qwen解析数据

## 🎯 题目类型格式

### 单选题
```json
{
  "question_type": "单选题",
  "option_a": "选项A",
  "option_b": "选项B", 
  "option_c": "选项C",
  "option_d": "选项D",
  "answer": {"correct": "A"}
}
```

### 多选题
```json
{
  "question_type": "多选题",
  "option_a": "选项A",
  "option_b": "选项B",
  "option_c": "选项C", 
  "option_d": "选项D",
  "answer": {"correct": ["A", "B"]}
}
```

### 判断题
```json
{
  "question_type": "判断题",
  "option_y": "正确",
  "option_n": "错误",
  "answer": {"correct": "Y"}
}
```

## 🚨 错误码

- `200`: 成功
- `201`: 创建成功
- `400`: 参数错误
- `404`: 资源不存在
- `500`: 服务器错误

## 💡 快速示例

### JavaScript调用
```javascript
// 查询题目
const response = await fetch('/api/v1/questions?page=1&page_size=10');
const result = await response.json();

// 创建题目
const newQuestion = await fetch('/api/v1/questions', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    cache_key_hash: 'unique_hash_123',
    question_type: '单选题',
    question_text: '这是一道测试题',
    option_a: '选项A',
    option_b: '选项B',
    answer: {correct: 'A'},
    user_image: 'https://example.com/img.jpg'
  })
});

// 更新题目
await fetch('/api/v1/questions/1', {
  method: 'PUT',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({
    question_text: '更新后的题目',
    is_verified: 1
  })
});

// 删除题目
await fetch('/api/v1/questions/1', {method: 'DELETE'});
```

### cURL示例
```bash
# 查询题目列表
curl "http://localhost:8080/api/v1/questions?page=1&page_size=10"

# 创建题目
curl -X POST http://localhost:8080/api/v1/questions \
  -H "Content-Type: application/json" \
  -d '{"cache_key_hash":"test123","question_type":"单选题","question_text":"测试题目","option_a":"A","option_b":"B","answer":{"correct":"A"},"user_image":"https://example.com/img.jpg"}'

# 更新题目
curl -X PUT http://localhost:8080/api/v1/questions/1 \
  -H "Content-Type: application/json" \
  -d '{"question_text":"更新后的题目","is_verified":1}'

# 删除题目
curl -X DELETE http://localhost:8080/api/v1/questions/1
```

---

**详细文档**: 请参考 `FRONTEND_API_GUIDE.md`  
**版本**: v1.0  
**更新**: 2025-06-12
