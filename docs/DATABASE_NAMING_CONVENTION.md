# 数据表命名规范文档

## 📋 概述

本文档定义了项目数据库表的命名规范，旨在为系统的长期扩展和维护提供清晰、一致的命名标准。

## 🎯 命名原则

### 1. 基本原则
- **模块前缀**: 使用业务模块前缀区分不同功能域
- **下划线分隔**: 使用下划线连接单词，提高可读性
- **复数形式**: 表名使用复数形式（如 users, questions）
- **英文命名**: 统一使用英文，避免中英混合
- **语义清晰**: 表名能够清楚表达业务含义
- **小写字母**: 所有表名使用小写字母

### 2. 命名结构
```
{module_prefix}_{business_entity}_{suffix}
```

**示例**:
- `core_questions` - 核心模块的题目表
- `user_profiles` - 用户模块的资料表
- `system_configs` - 系统模块的配置表

## 🏗️ 模块划分

### Core Module (核心业务模块)
**前缀**: `core_`
**用途**: 系统核心业务功能

| 表名 | 原表名 | 说明 |
|------|--------|------|
| `core_questions` | `questions` | 题目信息表 |
| `core_ai_model_configs` | `quest_model_config` | AI模型配置表 |

### User Module (用户管理模块)  
**前缀**: `user_`
**用途**: 用户相关功能

| 表名 | 说明 |
|------|------|
| `user_accounts` | 用户账户表 |
| `user_profiles` | 用户资料表 |
| `user_sessions` | 用户会话表 |
| `user_permissions` | 用户权限表 |
| `user_login_logs` | 用户登录日志表 |

### Content Module (内容管理模块)
**前缀**: `content_`
**用途**: 内容管理相关功能

| 表名 | 说明 |
|------|------|
| `content_categories` | 内容分类表 |
| `content_tags` | 内容标签表 |
| `content_attachments` | 内容附件表 |
| `content_versions` | 内容版本表 |
| `content_comments` | 内容评论表 |

### System Module (系统管理模块)
**前缀**: `system_`
**用途**: 系统管理和配置

| 表名 | 说明 |
|------|------|
| `system_configs` | 系统配置表 |
| `system_logs` | 系统日志表 |
| `system_notifications` | 系统通知表 |
| `system_tasks` | 系统任务表 |
| `system_health_checks` | 系统健康检查表 |

### Analytics Module (统计分析模块)
**前缀**: `analytics_`
**用途**: 数据统计和分析

| 表名 | 说明 |
|------|------|
| `analytics_user_actions` | 用户行为统计表 |
| `analytics_question_stats` | 题目统计分析表 |
| `analytics_performance` | 性能统计表 |
| `analytics_reports` | 分析报告表 |
| `analytics_metrics` | 指标统计表 |

### Cache Module (缓存管理模块)
**前缀**: `cache_`
**用途**: 缓存策略和管理

| 表名 | 说明 |
|------|------|
| `cache_strategies` | 缓存策略表 |
| `cache_statistics` | 缓存统计表 |
| `cache_invalidations` | 缓存失效记录表 |
| `cache_configurations` | 缓存配置表 |

## 🔧 字段命名规范

### 1. 主键字段
- 统一使用 `id` 作为主键字段名
- 类型: `BIGINT AUTO_INCREMENT`

### 2. 外键字段
- 格式: `{referenced_table_singular}_id`
- 示例: `user_id`, `question_id`, `category_id`

### 3. 时间字段
- `created_at` - 创建时间
- `updated_at` - 更新时间
- `deleted_at` - 软删除时间（如需要）

### 4. 状态字段
- `is_active` - 是否激活 (TINYINT, 0/1)
- `is_deleted` - 是否删除 (TINYINT, 0/1)
- `status` - 状态 (VARCHAR/ENUM)

### 5. 常用字段
- `name` - 名称
- `title` - 标题
- `description` - 描述
- `content` - 内容
- `type` - 类型
- `sort_order` - 排序

## 📝 索引命名规范

### 1. 普通索引
- 格式: `idx_{table_name}_{column_name}`
- 示例: `idx_core_questions_question_type`

### 2. 唯一索引
- 格式: `uk_{table_name}_{column_name}`
- 示例: `uk_user_accounts_email`

### 3. 外键索引
- 格式: `fk_{table_name}_{referenced_table}`
- 示例: `fk_user_profiles_user_accounts`

## 🚀 迁移策略

### 当前表重命名
1. **questions** → **core_questions**
2. **quest_model_config** → **core_ai_model_configs**

### 迁移步骤
1. 创建迁移脚本 `003_rename_tables_to_new_naming_convention.sql`
2. 更新模型文件中的 `TableName()` 方法
3. 更新相关的查询和引用
4. 测试验证迁移结果

## ✅ 最佳实践

1. **一致性**: 所有表名都遵循相同的命名规范
2. **可读性**: 表名应该能够清楚表达其用途
3. **可扩展性**: 模块化前缀便于未来功能扩展
4. **维护性**: 规范的命名便于团队协作和维护
5. **文档化**: 及时更新命名规范文档

## 📚 参考示例

```sql
-- 核心业务表
CREATE TABLE core_questions (...);
CREATE TABLE core_ai_model_configs (...);

-- 用户管理表
CREATE TABLE user_accounts (...);
CREATE TABLE user_profiles (...);

-- 系统管理表  
CREATE TABLE system_configs (...);
CREATE TABLE system_logs (...);

-- 统计分析表
CREATE TABLE analytics_user_actions (...);
CREATE TABLE analytics_question_stats (...);
```

---

**版本**: v1.0  
**更新时间**: 2025-06-11  
**维护者**: 开发团队
