package handler

import (
	"go-api-solve/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QuestionManagementHandler 题库管理处理器
type QuestionManagementHandler struct {
	questionMgmtService *service.QuestionManagementService
}

// NewQuestionManagementHandler 创建新的题库管理处理器
func NewQuestionManagementHandler(questionMgmtService *service.QuestionManagementService) *QuestionManagementHandler {
	return &QuestionManagementHandler{
		questionMgmtService: questionMgmtService,
	}
}

// ListQuestions 分页查询题库
// @Summary 分页查询题库
// @Description 支持多种条件过滤和分页查询题库
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(50)
// @Param question_type query string false "题目类型"
// @Param is_verified query int false "是否验证：0=未验证，1=已验证"
// @Param keyword query string false "搜索关键词"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Param sort query string false "排序字段" default(created_at)
// @Param order query string false "排序方向" default(desc)
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/questions [get]
func (h *QuestionManagementHandler) ListQuestions(c *gin.Context) {
	var params service.QuestionQueryParams
	
	// 绑定查询参数
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	// 查询数据
	result, err := h.questionMgmtService.ListQuestions(c.Request.Context(), &params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询题库失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    result,
	})
}

// GetQuestion 获取单个题目
// @Summary 获取单个题目
// @Description 根据ID获取题目详情
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/questions/{id} [get]
func (h *QuestionManagementHandler) GetQuestion(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
			"error":   err.Error(),
		})
		return
	}
	
	// 查询数据
	question, err := h.questionMgmtService.GetQuestion(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "题目不存在",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    question,
	})
}

// CreateQuestion 创建题目
// @Summary 创建题目
// @Description 创建新的题目
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param body body service.QuestionCreateRequest true "题目信息"
// @Success 201 {object} map[string]interface{}
// @Router /api/v1/questions [post]
func (h *QuestionManagementHandler) CreateQuestion(c *gin.Context) {
	var req service.QuestionCreateRequest
	
	// 绑定请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	// 创建题目
	question, err := h.questionMgmtService.CreateQuestion(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建题目失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    question,
	})
}

// UpdateQuestion 更新题目
// @Summary 更新题目
// @Description 更新题目信息（受限字段：id、qwen_raw、deepseek_raw、qwen_parsed不允许修改）
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Param body body service.QuestionUpdateRequest true "更新信息"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/questions/{id} [put]
func (h *QuestionManagementHandler) UpdateQuestion(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
			"error":   err.Error(),
		})
		return
	}
	
	// 绑定请求体
	var req service.QuestionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	// 更新题目
	question, err := h.questionMgmtService.UpdateQuestion(c.Request.Context(), id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新题目失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
		"data":    question,
	})
}

// DeleteQuestion 删除题目
// @Summary 删除题目
// @Description 物理删除题目
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/questions/{id} [delete]
func (h *QuestionManagementHandler) DeleteQuestion(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
			"error":   err.Error(),
		})
		return
	}
	
	// 删除题目
	if err := h.questionMgmtService.DeleteQuestion(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "删除题目失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "删除成功",
	})
}
