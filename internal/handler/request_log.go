package handler

import (
	"go-api-solve/internal/model"
	"go-api-solve/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// RequestLogHandler 请求日志处理器
type RequestLogHandler struct {
	requestLogService *service.RequestLogService
}

// NewRequestLogHandler 创建新的请求日志处理器
func NewRequestLogHandler(requestLogService *service.RequestLogService) *RequestLogHandler {
	return &RequestLogHandler{
		requestLogService: requestLogService,
	}
}

// ListRequestLogs 分页查询请求日志
// @Summary 分页查询请求日志
// @Description 支持多种条件过滤和分页查询请求日志
// @Tags 请求日志
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(50)
// @Param app_id query string false "应用ID"
// @Param user_id query int false "用户ID"
// @Param status query int false "状态：0=失败，1=成功"
// @Param is_manual_checked query int false "人工确认：0=未确认，1=已确认"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Param sort query string false "排序字段" default(created_at)
// @Param order query string false "排序方向" default(desc)
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/request-logs [get]
func (h *RequestLogHandler) ListRequestLogs(c *gin.Context) {
	var params model.RequestLogQueryParams
	
	// 绑定查询参数
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	// 查询数据
	result, err := h.requestLogService.ListRequestLogs(c.Request.Context(), &params)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "查询请求日志失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    result,
	})
}

// GetRequestLog 获取单个请求日志
// @Summary 获取单个请求日志
// @Description 根据ID获取请求日志详情
// @Tags 请求日志
// @Accept json
// @Produce json
// @Param id path int true "请求日志ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/request-logs/{id} [get]
func (h *RequestLogHandler) GetRequestLog(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
			"error":   err.Error(),
		})
		return
	}
	
	// 查询数据
	requestLog, err := h.requestLogService.GetRequestLog(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "请求日志不存在",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    requestLog,
	})
}

// UpdateManualCheckStatus 更新人工确认状态
// @Summary 更新人工确认状态
// @Description 更新请求日志的人工确认状态
// @Tags 请求日志
// @Accept json
// @Produce json
// @Param id path int true "请求日志ID"
// @Param body body model.RequestLogUpdateRequest true "更新参数"
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/request-logs/{id}/manual-check [put]
func (h *RequestLogHandler) UpdateManualCheckStatus(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的ID参数",
			"error":   err.Error(),
		})
		return
	}
	
	// 绑定请求体
	var req model.RequestLogUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	// 验证参数
	if req.IsManualChecked == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "is_manual_checked 参数不能为空",
		})
		return
	}
	
	if *req.IsManualChecked != 0 && *req.IsManualChecked != 1 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "is_manual_checked 参数必须为 0 或 1",
		})
		return
	}
	
	// 更新状态
	if err := h.requestLogService.UpdateManualCheckStatus(c.Request.Context(), id, *req.IsManualChecked); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "更新成功",
	})
}

// GetStatistics 获取统计信息
// @Summary 获取请求日志统计信息
// @Description 获取请求日志的统计信息，包括总数、成功率等
// @Tags 请求日志
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/request-logs/statistics [get]
func (h *RequestLogHandler) GetStatistics(c *gin.Context) {
	// 获取统计信息
	stats, err := h.requestLogService.GetStatistics(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取统计信息失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "查询成功",
		"data":    stats,
	})
}

// CreateRequestLog 创建请求日志（可选，主要用于测试）
// @Summary 创建请求日志
// @Description 手动创建请求日志记录
// @Tags 请求日志
// @Accept json
// @Produce json
// @Param body body model.RequestLogCreateRequest true "请求日志信息"
// @Success 201 {object} map[string]interface{}
// @Router /api/v1/request-logs [post]
func (h *RequestLogHandler) CreateRequestLog(c *gin.Context) {
	var req model.RequestLogCreateRequest
	
	// 绑定请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}
	
	// 创建请求日志
	requestLog, err := h.requestLogService.CreateRequestLog(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建请求日志失败",
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "创建成功",
		"data":    requestLog,
	})
}
