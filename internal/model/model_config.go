package model

import "time"

// ModelConfig 模型配置信息
type ModelConfig struct {
	ID                int       `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	ModelName         string    `json:"model_name" gorm:"type:varchar(100);not null;uniqueIndex;comment:模型名称"`
	RoleSystem        string    `json:"role_system" gorm:"type:text;not null;comment:system角色的Content"`
	RoleUser          string    `json:"role_user" gorm:"type:text;not null;comment:user角色的Content"`
	Temperature       float64   `json:"temperature" gorm:"type:decimal(3,2);default:0.00;comment:温度参数"`
	TopP              float64   `json:"top_p" gorm:"type:decimal(3,2);default:0.80;comment:TopP参数"`
	TopK              int       `json:"top_k" gorm:"type:int;default:50;comment:TopK参数"`
	RepetitionPenalty float64   `json:"repetition_penalty" gorm:"type:decimal(4,3);default:1.000;comment:重复惩罚"`
	PresencePenalty   float64   `json:"presence_penalty" gorm:"type:decimal(4,3);default:1.500;comment:存在惩罚"`
	ResponseFormat    string    `json:"response_format" gorm:"type:varchar(50);default:'json_object';comment:返回格式"`
	IsActive          int       `json:"is_active" gorm:"type:tinyint;default:1;index;comment:是否启用"`
	CreatedAt         time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt         time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 指定表名
func (ModelConfig) TableName() string {
	return "core_ai_model_configs"
}

// QwenRequest Qwen API请求结构
type QwenRequest struct {
	Model      string     `json:"model"`
	Input      QwenInput  `json:"input"`
	Parameters QwenParams `json:"parameters"`
}

// QwenInput Qwen输入结构
type QwenInput struct {
	Messages []QwenMessage `json:"messages"`
}

// QwenMessage Qwen消息结构
type QwenMessage struct {
	Role    string                   `json:"role"`
	Content []map[string]interface{} `json:"content"`
	Image   string                   `json:"image,omitempty"`
}

// QwenParams Qwen参数结构
type QwenParams struct {
	Temperature       float64 `json:"temperature"`
	TopP              float64 `json:"top_p"`
	TopK              int     `json:"top_k"`
	RepetitionPenalty float64 `json:"repetition_penalty"`
	PresencePenalty   float64 `json:"presence_penalty"`
	ResponseFormat    string  `json:"response_format"`
}

// QwenResponse Qwen API响应结构 - DashScope多模态API标准格式
type QwenResponse struct {
	Output QwenOutput `json:"output"`
	Usage  QwenUsage  `json:"usage"`
}

// QwenOutput Qwen输出结构 - 仅支持DashScope多模态API的choices格式
type QwenOutput struct {
	Choices []QwenChoice `json:"choices"`
}

// QwenChoice Qwen选择结构
type QwenChoice struct {
	Message      QwenMessage `json:"message"`
	FinishReason string      `json:"finish_reason"`
}

// QwenUsage Qwen使用量结构
type QwenUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// DeepseekRequest DeepSeek API请求结构
type DeepseekRequest struct {
	Messages         []DeepseekMessage `json:"messages"`
	Model            string            `json:"model"`
	FrequencyPenalty float64           `json:"frequency_penalty"`
	PresencePenalty  float64           `json:"presence_penalty"`
	ResponseFormat   ResponseFormat    `json:"response_format"`
	Temperature      float64           `json:"temperature"`
	TopP             float64           `json:"top_p"`
}

// DeepseekMessage DeepSeek消息结构
type DeepseekMessage struct {
	Content string `json:"content"`
	Role    string `json:"role"`
}

// ResponseFormat 响应格式结构
type ResponseFormat struct {
	Type string `json:"type"`
}

// DeepseekResponse DeepSeek API响应结构
type DeepseekResponse struct {
	ID      string           `json:"id"`
	Object  string           `json:"object"`
	Created int64            `json:"created"`
	Model   string           `json:"model"`
	Choices []DeepseekChoice `json:"choices"`
	Usage   DeepseekUsage    `json:"usage"`
}

// DeepseekChoice DeepSeek选择结构
type DeepseekChoice struct {
	Index        int             `json:"index"`
	Message      DeepseekMessage `json:"message"`
	FinishReason string          `json:"finish_reason"`
}

// DeepseekUsage DeepSeek使用量结构
type DeepseekUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}
