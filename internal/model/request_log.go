package model

import (
	"time"
)

// RequestLog 请求日志模型
type RequestLog struct {
	ID              int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	AppID           string    `json:"app_id" gorm:"type:varchar(100);not null;default:'1';index;comment:发起请求的应用ID（后期扩展使用）"`
	AppName         string    `json:"app_name" gorm:"type:varchar(100);not null;default:'1';comment:应用名称（后期扩展使用）"`
	UserID          int64     `json:"user_id" gorm:"not null;default:1;index;comment:关联的用户ID（后期扩展使用）"`
	ImageURL        string    `json:"image_url" gorm:"type:varchar(1000);not null;comment:应用请求时图片URL参数"`
	ResponsePayload JSONField `json:"response_payload,omitempty" gorm:"type:json;comment:API返回的数组数据"`
	QwenTokens      JSONField `json:"qwen_tokens,omitempty" gorm:"type:json;comment:Qwen模型消耗的token信息"`
	DeepseekTokens  JSONField `json:"deepseek_tokens,omitempty" gorm:"type:json;comment:DeepSeek模型消耗的token信息"`
	Status          int       `json:"status" gorm:"type:tinyint;default:1;index;comment:响应状态：1=成功，0=失败"`
	ErrorMessage    *string   `json:"error_message,omitempty" gorm:"type:text;comment:失败时的错误信息"`
	IsManualChecked int       `json:"is_manual_checked" gorm:"type:tinyint;default:0;index;comment:人工确认标记：0=未标记，1=确认"`
	CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime;index;comment:创建时间"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 指定表名
func (RequestLog) TableName() string {
	return "core_request_logs"
}

// TokenUsage Token使用量结构
type TokenUsage struct {
	InputTokens  int `json:"input_tokens,omitempty"`
	OutputTokens int `json:"output_tokens,omitempty"`
	TotalTokens  int `json:"total_tokens,omitempty"`
	// DeepSeek使用不同的字段名
	PromptTokens     int `json:"prompt_tokens,omitempty"`
	CompletionTokens int `json:"completion_tokens,omitempty"`
}

// RequestLogResponse 返回给用户的请求日志数据结构
type RequestLogResponse struct {
	ID              int64                  `json:"id"`
	AppID           string                 `json:"app_id"`
	AppName         string                 `json:"app_name"`
	UserID          int64                  `json:"user_id"`
	ImageURL        string                 `json:"image_url"`
	ResponsePayload map[string]interface{} `json:"response_payload,omitempty"`
	QwenTokens      map[string]interface{} `json:"qwen_tokens,omitempty"`
	DeepseekTokens  map[string]interface{} `json:"deepseek_tokens,omitempty"`
	Status          int                    `json:"status"`
	ErrorMessage    string                 `json:"error_message,omitempty"`
	IsManualChecked int                    `json:"is_manual_checked"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// RequestLogCreateRequest 创建请求日志的请求结构
type RequestLogCreateRequest struct {
	AppID           string                 `json:"app_id,omitempty"`           // 后期扩展使用，暂时默认为"1"
	AppName         string                 `json:"app_name,omitempty"`         // 后期扩展使用，暂时默认为"1"
	UserID          int64                  `json:"user_id,omitempty"`          // 后期扩展使用，暂时默认为1
	ImageURL        string                 `json:"image_url" binding:"required"`
	ResponsePayload map[string]interface{} `json:"response_payload,omitempty"`
	QwenTokens      map[string]interface{} `json:"qwen_tokens,omitempty"`
	DeepseekTokens  map[string]interface{} `json:"deepseek_tokens,omitempty"`
	Status          int                    `json:"status"`
	ErrorMessage    string                 `json:"error_message,omitempty"`
}

// RequestLogUpdateRequest 更新请求日志的请求结构
type RequestLogUpdateRequest struct {
	IsManualChecked *int `json:"is_manual_checked,omitempty"`
}

// RequestLogQueryParams 查询请求日志的参数结构
type RequestLogQueryParams struct {
	Page            int    `form:"page" binding:"omitempty,min=1"`
	PageSize        int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	AppID           string `form:"app_id"`
	UserID          int64  `form:"user_id"`
	Status          *int   `form:"status" binding:"omitempty,oneof=0 1"`
	IsManualChecked *int   `form:"is_manual_checked" binding:"omitempty,oneof=0 1"`
	StartDate       string `form:"start_date"` // 格式: 2006-01-02
	EndDate         string `form:"end_date"`   // 格式: 2006-01-02
	Sort            string `form:"sort" binding:"omitempty,oneof=created_at updated_at"`
	Order           string `form:"order" binding:"omitempty,oneof=asc desc"`
}

// GetDefaultValues 获取默认值（用于后期扩展）
func (r *RequestLogCreateRequest) GetDefaultValues() {
	if r.AppID == "" {
		r.AppID = "1" // 后期扩展使用
	}
	if r.AppName == "" {
		r.AppName = "1" // 后期扩展使用
	}
	if r.UserID == 0 {
		r.UserID = 1 // 后期扩展使用
	}
}

// GetDefaultQueryParams 获取默认查询参数
func (q *RequestLogQueryParams) GetDefaultQueryParams() {
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 50 // 默认分页大小为50
	}
	if q.Sort == "" {
		q.Sort = "created_at"
	}
	if q.Order == "" {
		q.Order = "desc"
	}
}
