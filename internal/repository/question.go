package repository

import (
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/utils"
	"time"

	"gorm.io/gorm"
)

// QuestionRepository 题目数据访问层
type QuestionRepository struct {
	db *gorm.DB
}

// NewQuestionRepository 创建新的题目仓库
func NewQuestionRepository(db *gorm.DB) *QuestionRepository {
	return &QuestionRepository{
		db: db,
	}
}

// GetByCacheKeyHash 根据缓存键哈希获取题目
func (r *QuestionRepository) GetByCacheKeyHash(cacheKeyHash string) ([]model.Question, error) {
	var questions []model.Question
	
	err := r.db.Where("cache_key_hash = ?", cacheKeyHash).Find(&questions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get questions by cache key hash: %w", err)
	}
	
	return questions, nil
}

// Create 创建新题目
func (r *QuestionRepository) Create(question *model.Question) error {
	err := r.db.Create(question).Error
	if err != nil {
		return fmt.Errorf("failed to create question: %w", err)
	}
	
	return nil
}

// Update 更新题目（使用字段映射）
func (r *QuestionRepository) Update(id int64, updates map[string]interface{}) error {
	result := r.db.Model(&model.Question{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update question: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("question not found with id: %d", id)
	}
	return nil
}

// UpdateModel 更新题目（使用模型）
func (r *QuestionRepository) UpdateModel(question *model.Question) error {
	err := r.db.Save(question).Error
	if err != nil {
		return fmt.Errorf("failed to update question: %w", err)
	}

	return nil
}

// GetByID 根据ID获取题目
func (r *QuestionRepository) GetByID(id int64) (*model.Question, error) {
	var question model.Question
	
	err := r.db.First(&question, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("question not found with id: %d", id)
		}
		return nil, fmt.Errorf("failed to get question by id: %w", err)
	}
	
	return &question, nil
}

// GetByImageURL 根据图片URL获取题目
func (r *QuestionRepository) GetByImageURL(imageURL string) ([]model.Question, error) {
	var questions []model.Question
	
	err := r.db.Where("image_url = ?", imageURL).Find(&questions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get questions by image URL: %w", err)
	}
	
	return questions, nil
}

// GetAll 获取所有题目（分页）
func (r *QuestionRepository) GetAll(offset, limit int) ([]model.Question, error) {
	var questions []model.Question
	
	err := r.db.Offset(offset).Limit(limit).Find(&questions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get all questions: %w", err)
	}
	
	return questions, nil
}

// Count 获取题目总数
func (r *QuestionRepository) Count() (int64, error) {
	var count int64
	
	err := r.db.Model(&model.Question{}).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count questions: %w", err)
	}
	
	return count, nil
}

// Delete 删除题目（物理删除）
func (r *QuestionRepository) Delete(id int64) error {
	result := r.db.Where("id = ?", id).Delete(&model.Question{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete question: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("question not found with id: %d", id)
	}
	return nil
}

// QuestionQueryInterface 题库查询参数接口（避免循环导入）
type QuestionQueryInterface interface {
	GetDefaultQueryParams()
	GetPage() int
	GetPageSize() int
	GetQuestionType() string
	GetIsVerified() *int
	GetKeyword() string
	GetStartDate() string
	GetEndDate() string
	GetSort() string
	GetOrder() string
}

// ListWithFilters 带过滤条件的分页查询
func (r *QuestionRepository) ListWithFilters(params QuestionQueryInterface) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 设置默认值
	params.GetDefaultQueryParams()

	// 构建查询条件
	query := r.db.Model(&model.Question{})

	// 添加过滤条件
	if params.GetQuestionType() != "" {
		query = query.Where("question_type = ?", params.GetQuestionType())
	}
	if params.GetIsVerified() != nil {
		query = query.Where("is_verified = ?", *params.GetIsVerified())
	}
	if params.GetKeyword() != "" {
		keyword := "%" + params.GetKeyword() + "%"
		query = query.Where("question_text LIKE ? OR analysis LIKE ?", keyword, keyword)
	}

	// 添加日期范围过滤
	if params.GetStartDate() != "" {
		startDate, err := time.Parse("2006-01-02", params.GetStartDate())
		if err == nil {
			query = query.Where("created_at >= ?", startDate)
		}
	}
	if params.GetEndDate() != "" {
		endDate, err := time.Parse("2006-01-02", params.GetEndDate())
		if err == nil {
			// 结束日期包含当天，所以加一天
			endDate = endDate.AddDate(0, 0, 1)
			query = query.Where("created_at < ?", endDate)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count questions: %w", err)
	}

	// 验证排序字段
	allowedSortFields := []string{"id", "created_at", "updated_at", "question_type", "is_verified"}
	if !utils.ValidateSortField(params.GetSort(), allowedSortFields) {
		// 使用默认排序
		params.GetDefaultQueryParams()
	}
	if !utils.ValidateOrderDirection(params.GetOrder()) {
		// 使用默认排序
		params.GetDefaultQueryParams()
	}

	// 分页查询
	paginationParams := utils.NewPaginationParams(params.GetPage(), params.GetPageSize(), params.GetSort(), params.GetOrder())

	err := query.
		Order(paginationParams.GetOrderBy()).
		Offset(paginationParams.GetOffset()).
		Limit(paginationParams.GetLimit()).
		Find(&questions).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to list questions with filters: %w", err)
	}

	return questions, total, nil
}
