package repository

import (
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/utils"
	"time"

	"gorm.io/gorm"
)

// RequestLogRepository 请求日志仓库
type RequestLogRepository struct {
	db *gorm.DB
}

// NewRequestLogRepository 创建新的请求日志仓库
func NewRequestLogRepository(db *gorm.DB) *RequestLogRepository {
	return &RequestLogRepository{
		db: db,
	}
}

// Create 创建请求日志
func (r *RequestLogRepository) Create(log *model.RequestLog) error {
	if err := r.db.Create(log).Error; err != nil {
		return fmt.Errorf("failed to create request log: %w", err)
	}
	return nil
}

// GetByID 根据ID获取请求日志
func (r *RequestLogRepository) GetByID(id int64) (*model.RequestLog, error) {
	var log model.RequestLog
	
	err := r.db.Where("id = ?", id).First(&log).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("request log not found with id: %d", id)
		}
		return nil, fmt.Errorf("failed to get request log: %w", err)
	}
	
	return &log, nil
}

// Update 更新请求日志
func (r *RequestLogRepository) Update(id int64, updates map[string]interface{}) error {
	result := r.db.Model(&model.RequestLog{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update request log: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("request log not found with id: %d", id)
	}
	return nil
}

// Delete 删除请求日志（物理删除）
func (r *RequestLogRepository) Delete(id int64) error {
	result := r.db.Where("id = ?", id).Delete(&model.RequestLog{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete request log: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("request log not found with id: %d", id)
	}
	return nil
}

// List 分页查询请求日志
func (r *RequestLogRepository) List(params *model.RequestLogQueryParams) ([]*model.RequestLog, int64, error) {
	var logs []*model.RequestLog
	var total int64
	
	// 设置默认值
	params.GetDefaultQueryParams()
	
	// 构建查询条件
	query := r.db.Model(&model.RequestLog{})
	
	// 添加过滤条件
	if params.AppID != "" {
		query = query.Where("app_id = ?", params.AppID)
	}
	if params.UserID > 0 {
		query = query.Where("user_id = ?", params.UserID)
	}
	if params.Status != nil {
		query = query.Where("status = ?", *params.Status)
	}
	if params.IsManualChecked != nil {
		query = query.Where("is_manual_checked = ?", *params.IsManualChecked)
	}
	
	// 添加日期范围过滤
	if params.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", params.StartDate)
		if err == nil {
			query = query.Where("created_at >= ?", startDate)
		}
	}
	if params.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", params.EndDate)
		if err == nil {
			// 结束日期包含当天，所以加一天
			endDate = endDate.AddDate(0, 0, 1)
			query = query.Where("created_at < ?", endDate)
		}
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count request logs: %w", err)
	}
	
	// 验证排序字段
	allowedSortFields := []string{"id", "created_at", "updated_at", "status", "app_id", "user_id"}
	if !utils.ValidateSortField(params.Sort, allowedSortFields) {
		params.Sort = "created_at"
	}
	if !utils.ValidateOrderDirection(params.Order) {
		params.Order = "desc"
	}
	
	// 分页查询
	paginationParams := utils.NewPaginationParams(params.Page, params.PageSize, params.Sort, params.Order)
	
	err := query.
		Order(paginationParams.GetOrderBy()).
		Offset(paginationParams.GetOffset()).
		Limit(paginationParams.GetLimit()).
		Find(&logs).Error
	
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list request logs: %w", err)
	}
	
	return logs, total, nil
}

// UpdateManualCheckStatus 更新人工确认状态
func (r *RequestLogRepository) UpdateManualCheckStatus(id int64, isChecked int) error {
	updates := map[string]interface{}{
		"is_manual_checked": isChecked,
		"updated_at":        time.Now(),
	}
	return r.Update(id, updates)
}

// GetStatistics 获取统计信息
func (r *RequestLogRepository) GetStatistics() (map[string]interface{}, error) {
	var stats map[string]interface{} = make(map[string]interface{})
	
	// 总请求数
	var totalRequests int64
	if err := r.db.Model(&model.RequestLog{}).Count(&totalRequests).Error; err != nil {
		return nil, fmt.Errorf("failed to count total requests: %w", err)
	}
	stats["total_requests"] = totalRequests
	
	// 成功请求数
	var successRequests int64
	if err := r.db.Model(&model.RequestLog{}).Where("status = ?", 1).Count(&successRequests).Error; err != nil {
		return nil, fmt.Errorf("failed to count success requests: %w", err)
	}
	stats["success_requests"] = successRequests
	
	// 失败请求数
	var failedRequests int64
	if err := r.db.Model(&model.RequestLog{}).Where("status = ?", 0).Count(&failedRequests).Error; err != nil {
		return nil, fmt.Errorf("failed to count failed requests: %w", err)
	}
	stats["failed_requests"] = failedRequests
	
	// 已人工确认数
	var checkedRequests int64
	if err := r.db.Model(&model.RequestLog{}).Where("is_manual_checked = ?", 1).Count(&checkedRequests).Error; err != nil {
		return nil, fmt.Errorf("failed to count checked requests: %w", err)
	}
	stats["checked_requests"] = checkedRequests
	
	// 成功率
	if totalRequests > 0 {
		stats["success_rate"] = float64(successRequests) / float64(totalRequests) * 100
	} else {
		stats["success_rate"] = 0.0
	}
	
	return stats, nil
}
