package service

import (
	"context"
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/repository"
	"go-api-solve/internal/utils"
	"log"
)

// RequestLogService 请求日志服务
type RequestLogService struct {
	requestLogRepo *repository.RequestLogRepository
}

// NewRequestLogService 创建新的请求日志服务
func NewRequestLogService(requestLogRepo *repository.RequestLogRepository) *RequestLogService {
	return &RequestLogService{
		requestLogRepo: requestLogRepo,
	}
}

// CreateRequestLog 创建请求日志
func (s *RequestLogService) CreateRequestLog(ctx context.Context, req *model.RequestLogCreateRequest) (*model.RequestLogResponse, error) {
	// 设置默认值（后期扩展使用）
	req.GetDefaultValues()
	
	// 转换为数据库模型
	requestLog := &model.RequestLog{
		AppID:           req.AppID,
		AppName:         req.AppName,
		UserID:          req.UserID,
		ImageURL:        req.ImageURL,
		ResponsePayload: model.JSONField(req.ResponsePayload),
		QwenTokens:      model.JSONField(req.QwenTokens),
		DeepseekTokens:  model.JSONField(req.DeepseekTokens),
		Status:          req.Status,
		IsManualChecked: 0, // 默认未确认
	}
	
	// 处理错误信息
	if req.ErrorMessage != "" {
		requestLog.ErrorMessage = &req.ErrorMessage
	}
	
	// 保存到数据库
	if err := s.requestLogRepo.Create(requestLog); err != nil {
		return nil, fmt.Errorf("failed to create request log: %w", err)
	}
	
	// 转换为响应格式
	return s.convertToResponse(requestLog), nil
}

// GetRequestLog 获取单个请求日志
func (s *RequestLogService) GetRequestLog(ctx context.Context, id int64) (*model.RequestLogResponse, error) {
	requestLog, err := s.requestLogRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get request log: %w", err)
	}
	
	return s.convertToResponse(requestLog), nil
}

// ListRequestLogs 分页查询请求日志
func (s *RequestLogService) ListRequestLogs(ctx context.Context, params *model.RequestLogQueryParams) (*utils.PaginationResult, error) {
	// 查询数据
	logs, total, err := s.requestLogRepo.List(params)
	if err != nil {
		return nil, fmt.Errorf("failed to list request logs: %w", err)
	}
	
	// 转换为响应格式
	var responses []*model.RequestLogResponse
	for _, log := range logs {
		responses = append(responses, s.convertToResponse(log))
	}
	
	// 创建分页参数
	paginationParams := utils.NewPaginationParams(params.Page, params.PageSize, params.Sort, params.Order)
	
	// 返回分页结果
	return utils.NewPaginationResult(responses, paginationParams, total), nil
}

// UpdateManualCheckStatus 更新人工确认状态
func (s *RequestLogService) UpdateManualCheckStatus(ctx context.Context, id int64, isChecked int) error {
	// 验证参数
	if isChecked != 0 && isChecked != 1 {
		return fmt.Errorf("invalid is_manual_checked value: %d, must be 0 or 1", isChecked)
	}
	
	// 更新状态
	if err := s.requestLogRepo.UpdateManualCheckStatus(id, isChecked); err != nil {
		return fmt.Errorf("failed to update manual check status: %w", err)
	}
	
	return nil
}

// GetStatistics 获取统计信息
func (s *RequestLogService) GetStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats, err := s.requestLogRepo.GetStatistics()
	if err != nil {
		return nil, fmt.Errorf("failed to get statistics: %w", err)
	}
	
	return stats, nil
}

// LogAPIRequest 记录API请求日志（用于集成到现有接口）
func (s *RequestLogService) LogAPIRequest(ctx context.Context, imageURL string, responseData interface{}, qwenTokens, deepseekTokens map[string]interface{}, status int, errorMsg string) {
	// 异步记录日志，不影响主业务流程
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Error logging API request: %v", r)
			}
		}()

		req := &model.RequestLogCreateRequest{
			ImageURL:       imageURL,
			QwenTokens:     qwenTokens,
			DeepseekTokens: deepseekTokens,
			Status:         status,
			ErrorMessage:   errorMsg,
		}

		// 安全地处理响应数据
		if responseData != nil {
			if respMap, ok := responseData.(map[string]interface{}); ok {
				req.ResponsePayload = respMap
			} else {
				// 如果不是map类型，尝试转换
				req.ResponsePayload = s.ConvertResponseToMap(responseData)
			}
		}

		_, err := s.CreateRequestLog(context.Background(), req)
		if err != nil {
			log.Printf("Failed to log API request: %v", err)
		}
	}()
}

// convertToResponse 转换为响应格式
func (s *RequestLogService) convertToResponse(log *model.RequestLog) *model.RequestLogResponse {
	response := &model.RequestLogResponse{
		ID:              log.ID,
		AppID:           log.AppID,
		AppName:         log.AppName,
		UserID:          log.UserID,
		ImageURL:        log.ImageURL,
		ResponsePayload: map[string]interface{}(log.ResponsePayload),
		QwenTokens:      map[string]interface{}(log.QwenTokens),
		DeepseekTokens:  map[string]interface{}(log.DeepseekTokens),
		Status:          log.Status,
		IsManualChecked: log.IsManualChecked,
		CreatedAt:       log.CreatedAt,
		UpdatedAt:       log.UpdatedAt,
	}
	
	// 处理错误信息
	if log.ErrorMessage != nil {
		response.ErrorMessage = *log.ErrorMessage
	}
	
	return response
}

// ExtractTokensFromQwenResponse 从Qwen响应中提取token信息
func (s *RequestLogService) ExtractTokensFromQwenResponse(response *model.QwenResponse) map[string]interface{} {
	if response == nil {
		return nil
	}
	
	return map[string]interface{}{
		"input_tokens":  response.Usage.InputTokens,
		"output_tokens": response.Usage.OutputTokens,
		"total_tokens":  response.Usage.TotalTokens,
	}
}

// ExtractTokensFromDeepseekResponse 从DeepSeek响应中提取token信息
func (s *RequestLogService) ExtractTokensFromDeepseekResponse(response *model.DeepseekResponse) map[string]interface{} {
	if response == nil {
		return nil
	}
	
	return map[string]interface{}{
		"prompt_tokens":     response.Usage.PromptTokens,
		"completion_tokens": response.Usage.CompletionTokens,
		"total_tokens":      response.Usage.TotalTokens,
	}
}

// ConvertResponseToMap 将响应数据转换为map格式
func (s *RequestLogService) ConvertResponseToMap(data interface{}) map[string]interface{} {
	// 先序列化再反序列化，确保类型转换正确
	jsonData, err := json.Marshal(data)
	if err != nil {
		log.Printf("Failed to marshal response data: %v", err)
		return nil
	}
	
	var result map[string]interface{}
	if err := json.Unmarshal(jsonData, &result); err != nil {
		log.Printf("Failed to unmarshal response data: %v", err)
		return nil
	}
	
	return result
}
