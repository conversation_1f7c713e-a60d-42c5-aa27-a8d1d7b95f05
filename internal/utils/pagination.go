package utils

import (
	"math"
)

// PaginationParams 分页参数
type PaginationParams struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Sort     string `json:"sort"`
	Order    string `json:"order"`
}

// PaginationResult 分页结果
type PaginationResult struct {
	Data       interface{} `json:"data"`
	Pagination Pagination  `json:"pagination"`
}

// Pagination 分页信息
type Pagination struct {
	Page       int   `json:"page"`        // 当前页码
	PageSize   int   `json:"page_size"`   // 每页大小
	Total      int64 `json:"total"`       // 总记录数
	TotalPages int   `json:"total_pages"` // 总页数
	HasNext    bool  `json:"has_next"`    // 是否有下一页
	HasPrev    bool  `json:"has_prev"`    // 是否有上一页
}

// NewPaginationParams 创建分页参数
func NewPaginationParams(page, pageSize int, sort, order string) *PaginationParams {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 50 // 默认分页大小
	}
	if pageSize > 100 {
		pageSize = 100 // 最大分页大小
	}
	if sort == "" {
		sort = "created_at"
	}
	if order == "" {
		order = "desc"
	}

	return &PaginationParams{
		Page:     page,
		PageSize: pageSize,
		Sort:     sort,
		Order:    order,
	}
}

// GetOffset 获取偏移量
func (p *PaginationParams) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// GetLimit 获取限制数量
func (p *PaginationParams) GetLimit() int {
	return p.PageSize
}

// GetOrderBy 获取排序字符串
func (p *PaginationParams) GetOrderBy() string {
	return p.Sort + " " + p.Order
}

// NewPaginationResult 创建分页结果
func NewPaginationResult(data interface{}, params *PaginationParams, total int64) *PaginationResult {
	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))
	
	pagination := Pagination{
		Page:       params.Page,
		PageSize:   params.PageSize,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    params.Page < totalPages,
		HasPrev:    params.Page > 1,
	}

	return &PaginationResult{
		Data:       data,
		Pagination: pagination,
	}
}

// ValidateSortField 验证排序字段是否合法
func ValidateSortField(field string, allowedFields []string) bool {
	for _, allowed := range allowedFields {
		if field == allowed {
			return true
		}
	}
	return false
}

// ValidateOrderDirection 验证排序方向是否合法
func ValidateOrderDirection(order string) bool {
	return order == "asc" || order == "desc"
}
