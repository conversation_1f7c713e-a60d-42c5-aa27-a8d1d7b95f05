-- 创建未来扩展功能的表结构示例
-- 这些表暂时不会被创建，仅作为规划参考

-- ============================================
-- 用户管理模块 (User Module)
-- ============================================

-- 用户账户表
-- CREATE TABLE IF NOT EXISTS user_accounts (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     username VARCHAR(50) NOT NULL COMMENT '用户名',
--     email VARCHAR(100) NOT NULL COMMENT '邮箱地址',
--     password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
--     phone VARCHAR(20) COMMENT '手机号码',
--     is_active TINYINT DEFAULT 1 COMMENT '是否激活',
--     is_verified TINYINT DEFAULT 0 COMMENT '是否已验证',
--     last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     
--     UNIQUE KEY uk_user_accounts_username (username) COMMENT '用户名唯一索引',
--     UNIQUE KEY uk_user_accounts_email (email) COMMENT '邮箱唯一索引',
--     INDEX idx_user_accounts_phone (phone) COMMENT '手机号索引',
--     INDEX idx_user_accounts_is_active (is_active) COMMENT '激活状态索引'
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表';

-- 用户资料表
-- CREATE TABLE IF NOT EXISTS user_profiles (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     user_id BIGINT NOT NULL COMMENT '用户ID',
--     nickname VARCHAR(50) COMMENT '昵称',
--     avatar_url VARCHAR(500) COMMENT '头像URL',
--     gender TINYINT COMMENT '性别：0未知，1男，2女',
--     birthday DATE COMMENT '生日',
--     bio TEXT COMMENT '个人简介',
--     location VARCHAR(100) COMMENT '所在地',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     
--     UNIQUE KEY uk_user_profiles_user_id (user_id) COMMENT '用户ID唯一索引',
--     INDEX idx_user_profiles_nickname (nickname) COMMENT '昵称索引'
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资料表';

-- ============================================
-- 系统管理模块 (System Module)
-- ============================================

-- 系统配置表
-- CREATE TABLE IF NOT EXISTS system_configs (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     config_key VARCHAR(100) NOT NULL COMMENT '配置键',
--     config_value TEXT COMMENT '配置值',
--     config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型：string,int,float,bool,json',
--     description TEXT COMMENT '配置描述',
--     is_active TINYINT DEFAULT 1 COMMENT '是否启用',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     
--     UNIQUE KEY uk_system_configs_key (config_key) COMMENT '配置键唯一索引',
--     INDEX idx_system_configs_type (config_type) COMMENT '配置类型索引',
--     INDEX idx_system_configs_is_active (is_active) COMMENT '启用状态索引'
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 系统日志表
-- CREATE TABLE IF NOT EXISTS system_logs (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     log_level VARCHAR(20) NOT NULL COMMENT '日志级别：DEBUG,INFO,WARN,ERROR,FATAL',
--     log_type VARCHAR(50) NOT NULL COMMENT '日志类型：API,DATABASE,CACHE,AI,SYSTEM',
--     message TEXT NOT NULL COMMENT '日志消息',
--     context JSON COMMENT '日志上下文数据',
--     user_id BIGINT COMMENT '相关用户ID',
--     ip_address VARCHAR(45) COMMENT 'IP地址',
--     user_agent TEXT COMMENT '用户代理',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     
--     INDEX idx_system_logs_level (log_level) COMMENT '日志级别索引',
--     INDEX idx_system_logs_type (log_type) COMMENT '日志类型索引',
--     INDEX idx_system_logs_user_id (user_id) COMMENT '用户ID索引',
--     INDEX idx_system_logs_created_at (created_at) COMMENT '创建时间索引'
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- ============================================
-- 统计分析模块 (Analytics Module)
-- ============================================

-- 用户行为统计表
-- CREATE TABLE IF NOT EXISTS analytics_user_actions (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     user_id BIGINT COMMENT '用户ID',
--     action_type VARCHAR(50) NOT NULL COMMENT '行为类型：VIEW,SUBMIT,DOWNLOAD,SHARE',
--     resource_type VARCHAR(50) NOT NULL COMMENT '资源类型：QUESTION,IMAGE,RESULT',
--     resource_id BIGINT COMMENT '资源ID',
--     session_id VARCHAR(100) COMMENT '会话ID',
--     ip_address VARCHAR(45) COMMENT 'IP地址',
--     user_agent TEXT COMMENT '用户代理',
--     metadata JSON COMMENT '额外元数据',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     
--     INDEX idx_analytics_user_actions_user_id (user_id) COMMENT '用户ID索引',
--     INDEX idx_analytics_user_actions_action_type (action_type) COMMENT '行为类型索引',
--     INDEX idx_analytics_user_actions_resource (resource_type, resource_id) COMMENT '资源索引',
--     INDEX idx_analytics_user_actions_created_at (created_at) COMMENT '创建时间索引'
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为统计表';

-- 题目统计分析表
-- CREATE TABLE IF NOT EXISTS analytics_question_stats (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     question_id BIGINT NOT NULL COMMENT '题目ID',
--     total_requests INT DEFAULT 0 COMMENT '总请求次数',
--     cache_hits INT DEFAULT 0 COMMENT '缓存命中次数',
--     cache_misses INT DEFAULT 0 COMMENT '缓存未命中次数',
--     avg_response_time DECIMAL(8,3) DEFAULT 0.000 COMMENT '平均响应时间(秒)',
--     success_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率(%)',
--     last_accessed_at TIMESTAMP NULL COMMENT '最后访问时间',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     
--     UNIQUE KEY uk_analytics_question_stats_question_id (question_id) COMMENT '题目ID唯一索引',
--     INDEX idx_analytics_question_stats_total_requests (total_requests) COMMENT '总请求次数索引',
--     INDEX idx_analytics_question_stats_last_accessed (last_accessed_at) COMMENT '最后访问时间索引'
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目统计分析表';

-- ============================================
-- 缓存管理模块 (Cache Module)  
-- ============================================

-- 缓存策略表
-- CREATE TABLE IF NOT EXISTS cache_strategies (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     strategy_name VARCHAR(100) NOT NULL COMMENT '策略名称',
--     cache_type VARCHAR(50) NOT NULL COMMENT '缓存类型：REDIS,MEMORY,FILE',
--     ttl_seconds INT DEFAULT 3600 COMMENT '过期时间(秒)',
--     max_size_mb INT DEFAULT 100 COMMENT '最大缓存大小(MB)',
--     eviction_policy VARCHAR(20) DEFAULT 'LRU' COMMENT '淘汰策略：LRU,LFU,FIFO',
--     is_active TINYINT DEFAULT 1 COMMENT '是否启用',
--     description TEXT COMMENT '策略描述',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     
--     UNIQUE KEY uk_cache_strategies_name (strategy_name) COMMENT '策略名称唯一索引',
--     INDEX idx_cache_strategies_type (cache_type) COMMENT '缓存类型索引',
--     INDEX idx_cache_strategies_is_active (is_active) COMMENT '启用状态索引'
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='缓存策略表';
