-- 创建请求日志表
-- 记录API请求的详细信息，包括token消耗、响应状态等
CREATE TABLE IF NOT EXISTS core_request_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    app_id VARCHAR(100) NOT NULL DEFAULT '1' COMMENT '发起请求的应用ID（后期扩展使用）',
    app_name VARCHAR(100) NOT NULL DEFAULT '1' COMMENT '应用名称（后期扩展使用）',
    user_id BIGINT NOT NULL DEFAULT 1 COMMENT '关联的用户ID（后期扩展使用）',
    image_url VARCHAR(1000) NOT NULL COMMENT '应用请求时图片URL参数',
    response_payload JSON COMMENT 'API返回的数组数据',
    qwen_tokens JSON COMMENT 'Qwen模型消耗的token信息',
    deepseek_tokens JSON COMMENT 'DeepSeek模型消耗的token信息',
    status TINYINT DEFAULT 1 COMMENT '响应状态：1=成功，0=失败',
    error_message TEXT COMMENT '失败时的错误信息',
    is_manual_checked TINYINT DEFAULT 0 COMMENT '人工确认标记：0=未标记，1=确认',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_core_request_logs_app_id (app_id) COMMENT '应用ID索引',
    INDEX idx_core_request_logs_user_id (user_id) COMMENT '用户ID索引',
    INDEX idx_core_request_logs_status (status) COMMENT '状态索引',
    INDEX idx_core_request_logs_is_manual_checked (is_manual_checked) COMMENT '人工确认索引',
    INDEX idx_core_request_logs_created_at (created_at) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='请求日志表';
