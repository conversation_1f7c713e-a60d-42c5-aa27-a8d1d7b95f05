# 我计划在现在的API业务上扩展开发多应用系统（单层租户架构）。这并不是真正意义上的多租户系统。或许我们可以称它为支持多调用端的 API 平台。在我的设计里，“App 是用户创建的调用凭证容器”，它确实具备轻量级“租户”的边界功能，可以理解为一种“子租户”或者“API级租户单位”。


## 用户权限相关；

- 用户表字段user表
1. id = 用户ID
2. phone = 用户注册手机号
3. password = 用户密码  （密码哈希）
2. name = 用户名称
3. token_balance = 租户余额
4. qps_limit = 用户默认QPS （用户在创建应用时，会将qps_limit赋值给应用。）
6. remark = 管理员备注字段 （非必填）
5. status = 用户状态（正常、冻结）
6. created_at = 用户创建时间
7. ddeleted_at = 用户更新时间


- 用户注册与登录
1. 手机号+用户名称+密码+短信验证码验证  == 注册申请 ；//注册申请后需要管理员审核后才可注册成功
2. 手机号+密码登录；
3. 手机号+短信验证码重置密码；

- 用户字段的详细解释
1. 用户名称，注册时设置，仅允许中文输入，用于显示在控制台等地方，不支持修改。
2. 密码，使用bcrypt加密存储。
3. 电话号码，用于登录和重置密码，不支持修改。
4. token_balance，用于控制用户可以调用API的次数，每次调用API都会扣除一定的token_balance，如果token_balance不足则无法调用API。token_balance的获取方式暂不考虑。
5. qps_limit，用于控制用户在创建应用时应用的QPS，被用户创建的应用会将用户的qps赋值过去。用户的qps_limit的值可以由管理员在后台进行调整。
6. status，用于控制用户的状态，正常状态下该用户创建的所有应用都可以调用API，冻结状态下所有应用都不可以调用API。
7. remark，管理员备注字段，用于记录一些额外的信息，非必填。


### 用户日志相关

- 注册记录表 user_register_logs
1. id = 主键，自增
2. phone = 注册手机号
3. name = 注册时填写的用户名（中文）
4. status = 状态：0=待审核, 1=通过, 2=拒绝
5. reason = 审核拒绝原因（仅在 status=2 时填写）
6. user_id = 审核通过后创建的 users.id，用于关联
7. created_at = 注册申请时间
8. reviewed_at = 审核时间（为空表示尚未审核）

- 用户注册表使用说明
1. 用户注册时写入该表
2. status=0（待审核）
3. 管理员审核通过
4. 创建 users 表记录，写入 user_id，更新 status=1，reviewed_at=NOW()
5. 管理员审核拒绝
6. 写 reason 字段，status=2，不创建用户
7. 后台审核页面可分页查询
8. 展示手机号、注册时间、状态、审核时间等

- 用户操作日志表user_operation_logs

1. id = 主键，自增
2. user_id = 操作用户 ID
3. action = 操作类型，如 login/create_app/reset_key',
4. target_type  = 操作目标实体类型，如 app/user/token',
5. target_id = 目标实体的 ID，如 AppID 或 UserID',
6. description = 可读性描述，比如“重置了 App 的密钥”',
7. created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',


- 使用说明
1. 用户1001登录成功了。
2. 用户1001创建一个应用驾考宝典
3. 用户1001重置XX应用的密钥
4. 用户1001的xx应用消耗了xxx token_balance



## 应用相关
- 用户应用表字段apps
CREATE TABLE apps (
1. id = 应用 ID
2. user_id = 所属用户 ID
3. name  = 应用名称 由用户创建时设置
4. app_key = 公开key 用于鉴权
5. app_secret = 私钥，用于签名/校验
6. qps_limit = QPS 限制（每秒请求数）创建时由用户表带入
7. used_this_month  = 本月已用 Token
8. status = 状态 1=启用，0=禁用 用户无法控制，由管理员控制
9. description = 应用说明或备注
10. ip_whitelist = IP 白名单
12. created_at = 创建时间
13. updated_at = 更新时间

- 用户应用信息表使用说明
1. 用户创建应用时写入该表，需要写入应用名称与备注信息
2. 创建成功后，生成 app_key 和 app_secret
3. 用户可以重置 app_secret
4. 用户无法修改 app_key
5. 用户无法修改 qps_limit，且用户不可见qps_limit的值
6. 用户无法修改 used_this_month，该字段由系统自动更新
7. 用户必须设置白名单，才可正常使用应用，白名单可以要求在创建时直接设置，后期也可以修改。





- 其他备注
1. 注册提交（手机号+名称+密码+短信验证码） → 用户表写入 status=3（待审核） → 管理员后台审核通过 → status=1（正常）



## 管理员权限相关

- 管理员admin表
1. id  = 主键，自增
2. username = 管理员账号
3. password_hash = 哈希密码
4. name = 显示名
5. role = 管理员角色 super-超级管理员,editor- 题库管理员
5. last_login_at = 最后登录时间
6. created_at
7. updated_at


- 超级管理员使用说明
1. 管理员可以审核用户账号。
2. 管理员可以对用户的应用进行冻结与恢复。
3. 管理员可以对用户的token_balance进行充值与扣除。
4. 管理员可以修改用户的qps_limit 。
5. 管理员可以对用户的应用独立设置qps_limit 。
6. 管理员可以对用户进行冻结与恢复（用户被冻结后所有应用也会被冻结）
7. 管理员可以查看用户的应用列表以及用户应用的所有信息包括日志与密钥。
8. 管理员可以修改用户名字
9. 管理员可以修改用户应用的白名单。
10. 管理员对题库进行增删改查
10. 超级管理员拥有系统完整的控制权
11. 超级管理员与题库管理员不可以自己注册，也不需要权限设置，直接固定权限即可。开发时直接生成这两个管理员即可。允许使用原密码修改密码。不支持重置密码。

- 题库管理员使用说明
1. 仅可以对题库表以及访问请求日志表（请求日志表参阅S2.md文档）进行增删改查，没有其他任何权限。
2. 其他无其他任何权限




- 其他问题处理
在之前的一次开发中，我们存在一些遗留问题；
在请求日志的方法中，appid、appname、userid、这几个值之前默认传入传入了1.现在需要在新的架构中，将其进行完善。